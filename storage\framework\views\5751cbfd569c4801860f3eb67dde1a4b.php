<div class="table-responsive p-0">
    <table id="<?php echo e($id); ?>" class="table align-items-center mb-0 <?php echo e($classes ?? ''); ?>" style="width: 100%;">
        <thead>
            <tr>
                <?php echo e($header); ?>

            </tr>
        </thead>
        <tbody>
            <?php echo e($slot); ?>

        </tbody>
    </table>
</div>

<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/link/dataTables.bootstrap5.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/link/responsive.bootstrap5.min.css')); ?>">

    <style>
        .dataTables_length select {
            min-width: 4.2rem !important;
        }

        .dataTables_wrapper td {
            vertical-align: middle;
        }

        .paginate_button {
            padding: 0px 1px;
        }
    </style>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/script/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/script/dataTables.bootstrap5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/script/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/script/responsive.bootstrap5.min.js')); ?>"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (!$.fn.dataTable.isDataTable('#<?php echo e($id); ?>')) {
                $('#<?php echo e($id); ?>').DataTable({
                    // responsive: true,
                    scrollX: true,
                    paging: true,
                    searching: true,
                    info: true,
                    autoWidth: false,
                    pageLength: 10,
                    lengthMenu: [10, 50, 100, 500],
                    processing: true,
                    serverSide: true,
                    ajax: "<?php echo e($ajax); ?>",
                    columns: <?php echo json_encode($columns, 15, 512) ?>,
                    order: <?php echo json_encode($order, 15, 512) ?>,
                    language: {
                        paginate: {
                            previous: '<i class="fas fa-angle-left"></i>',
                            next: '<i class="fas fa-angle-right"></i>'
                        },
                        lengthMenu: "Show _MENU_ entries",
                        search: "_INPUT_",
                        searchPlaceholder: "Search..."
                    },
                    dom: "<'row mb-3'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                        "<'table-responsive'tr>" +
                        "<'row mt-3'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>"
                });
            } else {
                $('#<?php echo e($id); ?>').DataTable().ajax.reload();
            }
        });
    </script>
<?php $__env->stopPush(); ?>








<?php /**PATH C:\xampp\htdocs\trash\resources\views/components/data-table.blade.php ENDPATH**/ ?>