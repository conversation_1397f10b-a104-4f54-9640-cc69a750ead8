@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Dashboard'])

    <div class="container-fluid py-4">
        <!-- Welcome Message - Admin Theme -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-light shadow-sm animate__animated animate__fadeIn bg-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle p-3 me-3" style="background-color: #f8f9fa;">
                                    <i class="fas fa-user-circle fa-2x" style="color: #67748e;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h4 class="fw-bold mb-1" style="color: #67748e;">Welcome, {{ Auth::user()->name }}</h4>
                                <p class="mb-0" style="color: #8392ab;">You're logged into the admin dashboard. Here's a quick overview.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Metrics - Admin Theme Cards -->
        <div class="row g-4">
            @php
                $cards = [
                    ['icon' => 'fa-layer-group', 'title' => 'Total Plans', 'count' => $totalPlans, 'color' => '#3085d6'],
                    ['icon' => 'fa-envelope', 'title' => 'Total Contact Forms', 'count' => $totalContacts, 'color' => '#28a745'],
                    ['icon' => 'fa-eye', 'title' => 'Viewed Contact Forms', 'count' => $viewedContacts, 'color' => '#17a2b8'],
                    ['icon' => 'fa-clock', 'title' => 'Pending Contact Forms', 'count' => $pendingContacts, 'color' => '#ffc107'],
                    ['icon' => 'fa-users', 'title' => 'Total Subscriptions', 'count' => $totalSubscriptions, 'color' => '#6f42c1'],
                    ['icon' => 'fa-check-circle', 'title' => 'Active Subscriptions', 'count' => $activeSubscriptions, 'color' => '#20c997'],
                    ['icon' => 'fa-credit-card', 'title' => 'Total Payments', 'count' => $totalPayments, 'color' => '#fd7e14'],
                    ['icon' => 'fa-dollar-sign', 'title' => 'Successful Payments', 'count' => $successfulPayments, 'color' => '#198754'],
                ];
            @endphp

            @foreach ($cards as $index => $card)
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 animate__animated animate__fadeInUp" style="animation-delay: {{ $index * 0.05 }}s">
                    <div class="card h-100 border-light shadow-sm overflow-hidden">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="rounded p-3 me-3" style="background-color: {{ $card['color'] }}15;">
                                        <i class="fas {{ $card['icon'] }} fa-lg" style="color: {{ $card['color'] }};"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1" style="color: #8392ab;">{{ $card['title'] }}</h6>
                                    <h4 class="fw-bold mb-0" style="color: #67748e;">{{ $card['count'] }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endsection