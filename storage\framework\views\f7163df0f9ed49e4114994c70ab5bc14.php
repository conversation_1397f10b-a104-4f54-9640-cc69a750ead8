<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

    .auth-toast-container {
        position: fixed;
        top: 24px;
        right: 24px;
        z-index: 10000;
        max-width: 400px;
        width: 100%;
        pointer-events: none;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }

    .auth-toast {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        margin-bottom: 12px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
        border: 1px solid rgba(255, 255, 255, 0.18);
        border-radius: 12px;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.12),
            0 2px 6px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        color: #374151;
        opacity: 0;
        transform: translateX(100%) scale(0.9);
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        pointer-events: auto;
        overflow: hidden;
        animation: slideInBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    }

    @keyframes slideInBounce {
        0% {
            opacity: 0;
            transform: translateX(100%) scale(0.8) rotate(10deg);
        }
        60% {
            opacity: 1;
            transform: translateX(-5%) scale(1.02) rotate(-2deg);
        }
        80% {
            transform: translateX(2%) scale(0.98) rotate(1deg);
        }
        100% {
            opacity: 1;
            transform: translateX(0) scale(1) rotate(0deg);
        }
    }

    @keyframes slideOutBounce {
        0% {
            opacity: 1;
            transform: translateX(0) scale(1) rotate(0deg);
        }
        100% {
            opacity: 0;
            transform: translateX(100%) scale(0.8) rotate(-10deg);
        }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .auth-toast.show {
        opacity: 1;
        transform: translateX(0) scale(1);
    }

    .auth-toast.hide {
        animation: slideOutBounce 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
    }



    .auth-toast-success {
        --toast-color: #10b981;
        border-left: 4px solid #10b981;
    }

    .auth-toast-error {
        --toast-color: #ef4444;
        border-left: 4px solid #ef4444;
    }

    .auth-toast-info {
        --toast-color: #3b82f6;
        border-left: 4px solid #3b82f6;
    }

    .auth-toast-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin-right: 16px;
        border-radius: 10px;
        flex-shrink: 0;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        animation: iconPulse 2s ease-in-out infinite;
    }

    @keyframes iconPulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(var(--icon-rgb), 0.4);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 0 8px rgba(var(--icon-rgb), 0);
        }
    }

    .auth-toast:hover .auth-toast-icon {
        transform: scale(1.1);
        animation-play-state: paused;
    }

    .auth-toast-success .auth-toast-icon {
        --icon-rgb: 16, 185, 129;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .auth-toast-error .auth-toast-icon {
        --icon-rgb: 239, 68, 68;
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .auth-toast-info .auth-toast-icon {
        --icon-rgb: 59, 130, 246;
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .auth-toast-icon svg {
        width: 20px;
        height: 20px;
        stroke-width: 2.5;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    .auth-toast-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .auth-toast-title {
        font-size: 15px;
        font-weight: 600;
        line-height: 1.4;
        margin: 0;
        color: #111827;
    }

    .auth-toast-message {
        font-size: 14px;
        line-height: 1.5;
        font-weight: 400;
        margin: 0;
        color: #6b7280;
    }

    .auth-toast-close {
        background: transparent;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        margin-left: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
    }

    .auth-toast-close:hover {
        color: #374151;
        background: rgba(0, 0, 0, 0.05);
        transform: scale(1.1);
    }

    .auth-toast-close:active {
        transform: scale(0.95);
    }

    .auth-toast-close svg {
        width: 16px;
        height: 16px;
        stroke-width: 2;
    }

    .auth-toast-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: var(--toast-color);
        border-radius: 0 0 12px 12px;
        transform-origin: left;
        animation: progressBar 7s linear forwards;
    }

    @keyframes progressBar {
        from { width: 100%; }
        to { width: 0%; }
    }

    @media (max-width: 768px) {
        .auth-toast-container {
            top: 16px;
            right: 16px;
            left: 16px;
            max-width: none;
        }

        .auth-toast {
            padding: 14px 16px;
            margin-bottom: 10px;
            border-radius: 10px;
        }

        .auth-toast-icon {
            width: 36px;
            height: 36px;
            margin-right: 12px;
            border-radius: 8px;
        }

        .auth-toast-icon svg {
            width: 18px;
            height: 18px;
        }

        .auth-toast-title {
            font-size: 14px;
        }

        .auth-toast-message {
            font-size: 13px;
        }

        .auth-toast-close {
            width: 28px;
            height: 28px;
            padding: 6px;
        }

        .auth-toast-close svg {
            width: 14px;
            height: 14px;
        }
    }

    @media (prefers-reduced-motion: reduce) {
        .auth-toast {
            animation: none;
            transition: opacity 0.3s ease;
        }

        .auth-toast.hide {
            animation: none;
        }

        .auth-toast-icon {
            animation: none;
        }

        .auth-toast:hover .auth-toast-icon {
            transform: none;
        }

        .auth-toast-progress {
            animation: none;
        }
    }

    @media (hover: hover) {
        .auth-toast:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                0 4px 8px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .auth-toast:hover .auth-toast-progress {
            animation-play-state: paused;
        }
    }

</style>

<!-- Elegant Auth Toast Container -->
<div id="auth-toast-container" class="auth-toast-container">
    <?php $__currentLoopData = ['success', 'error', 'info', 'toast_error', 'toast_success']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(session($type)): ?>
            <?php
                $toastClass = [
                    'success' => 'auth-toast-success',
                    'error' => 'auth-toast-error',
                    'info' => 'auth-toast-info',
                    'toast_error' => 'auth-toast-error',
                    'toast_success' => 'auth-toast-success',
                ][$type];

                $iconType = in_array($type, ['success', 'toast_success']) ? 'success' :
                           (in_array($type, ['error', 'toast_error']) ? 'error' : 'info');

                $titles = [
                    'success' => 'Success!',
                    'error' => 'Error!',
                    'info' => 'Information'
                ];
            ?>

            <div class="auth-toast <?php echo e($toastClass); ?>" data-type="<?php echo e($type); ?>">
                <!-- Icon -->
                <div class="auth-toast-icon">
                    <?php if($iconType === 'success'): ?>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    <?php elseif($iconType === 'error'): ?>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    <?php else: ?>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    <?php endif; ?>
                </div>

                <!-- Content -->
                <div class="auth-toast-content">
                    <h4 class="auth-toast-title"><?php echo e($titles[$iconType]); ?></h4>
                    <p class="auth-toast-message"><?php echo e(session($type)); ?></p>
                </div>

                <!-- Close Button -->
                <button class="auth-toast-close" onclick="closeAuthToast(this)" aria-label="Close notification">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <!-- Progress Bar -->
                <div class="auth-toast-progress"></div>
            </div>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<script>
// Modern Auth Toast functionality
function closeAuthToast(button) {
    const toast = button.closest('.auth-toast');
    const progressBar = toast.querySelector('.auth-toast-progress');

    // Pause progress bar animation
    if (progressBar) {
        progressBar.style.animationPlayState = 'paused';
    }

    toast.classList.add('hide');
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 400);
}

// Show auth toasts on page load with modern animations
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.auth-toast');

    toasts.forEach((toast, index) => {
        // Stagger the appearance of multiple toasts
        setTimeout(() => {
            toast.classList.add('show');

            // Add hover pause functionality for progress bar
            const progressBar = toast.querySelector('.auth-toast-progress');
            if (progressBar) {
                toast.addEventListener('mouseenter', () => {
                    progressBar.style.animationPlayState = 'paused';
                });

                toast.addEventListener('mouseleave', () => {
                    progressBar.style.animationPlayState = 'running';
                });
            }

            // Auto-hide after 7 seconds
            setTimeout(() => {
                if (toast.parentNode && !toast.matches(':hover')) {
                    toast.classList.add('hide');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 7000);
        }, index * 150); // 150ms delay between each toast
    });
});

// Function to create dynamic auth toasts (for JavaScript usage)
function showAuthToast(message, type = 'info', title = null) {
    const container = document.getElementById('auth-toast-container');
    if (!container) return;

    const config = {
        success: {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                   </svg>`,
            title: 'Success!'
        },
        error: {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                   </svg>`,
            title: 'Error!'
        },
        info: {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                   </svg>`,
            title: 'Information'
        }
    };

    const toastConfig = config[type] || config.info;
    const toastTitle = title || toastConfig.title;

    const toast = document.createElement('div');
    toast.className = `auth-toast auth-toast-${type}`;
    toast.innerHTML = `
        <div class="auth-toast-icon">${toastConfig.icon}</div>
        <div class="auth-toast-content">
            <h4 class="auth-toast-title">${toastTitle}</h4>
            <p class="auth-toast-message">${message}</p>
        </div>
        <button class="auth-toast-close" onclick="closeAuthToast(this)" aria-label="Close notification">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
        <div class="auth-toast-progress"></div>
    `;

    container.appendChild(toast);

    // Add hover pause functionality for progress bar
    const progressBar = toast.querySelector('.auth-toast-progress');
    if (progressBar) {
        toast.addEventListener('mouseenter', () => {
            progressBar.style.animationPlayState = 'paused';
        });

        toast.addEventListener('mouseleave', () => {
            progressBar.style.animationPlayState = 'running';
        });
    }

    // Show toast with smooth animation
    requestAnimationFrame(() => {
        setTimeout(() => {
            toast.classList.add('show');
        }, 50);
    });

    // Auto-hide after 7 seconds
    setTimeout(() => {
        if (toast.parentNode && !toast.matches(':hover')) {
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        }
    }, 7000);
}
</script>
<?php /**PATH C:\xampp\htdocs\trash\resources\views/components/auth-toast.blade.php ENDPATH**/ ?>