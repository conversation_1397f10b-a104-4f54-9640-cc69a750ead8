@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-shopping-cart"></i> Subscribe to {{ $plan->name }}
                    </h4>
                </div>
                <div class="card-body">
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="plan-details">
                                <h3>{{ $plan->name }}</h3>
                                @if($plan->description)
                                    <p class="text-muted">{{ $plan->description }}</p>
                                @endif

                                <div class="price-display mb-4">
                                    <span class="h2 text-primary">
                                        ${{ number_format($plan->price, 0) }}
                                    </span>
                                    <span class="text-muted">
                                        /{{ $plan->duration === 'yearly' ? 'year' : ($plan->duration === 'monthly' ? 'month' : 'week') }}
                                    </span>
                                </div>

                                @if($plan->services && $plan->services->count() > 0)
                                    <div class="features-list">
                                        <h5>What's included:</h5>
                                        <ul class="list-unstyled">
                                            @foreach($plan->services as $service)
                                                <li class="mb-2">
                                                    <i class="fas fa-check text-success me-2"></i>
                                                    {{ $service->name }}
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="subscription-form">
                                <h5>Subscription Summary</h5>
                                
                                <div class="summary-card p-3 bg-light rounded mb-4">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Plan:</span>
                                        <strong>{{ $plan->name }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Billing:</span>
                                        <strong>{{ ucfirst($plan->duration) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Amount:</span>
                                        <strong>${{ number_format($plan->price, 2) }}</strong>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Total:</strong></span>
                                        <strong class="text-primary">${{ number_format($plan->price, 2) }}</strong>
                                    </div>
                                </div>

                                <div class="payment-info mb-4">
                                    <h6>Payment Information</h6>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        You will be redirected to Stripe's secure checkout to complete your payment.
                                        Your subscription will auto-renew unless cancelled.
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" id="checkout-button" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card"></i>
                                        Subscribe Now - ${{ number_format($plan->price, 2) }}
                                    </button>
                                    <a href="{{ route('pricing') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Plans
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Processing your subscription...</h5>
                <p class="text-muted">Please wait while we redirect you to secure checkout.</p>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkoutButton = document.getElementById('checkout-button');
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));

    checkoutButton.addEventListener('click', function() {
        // Show loading modal
        loadingModal.show();
        
        // Disable button
        checkoutButton.disabled = true;
        checkoutButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        // Create checkout session
        fetch('{{ route("user.subscriptions.create-checkout-session") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                plan_id: '{{ $plan->plans_id }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to Stripe checkout
                window.location.href = data.checkout_url;
            } else {
                // Hide loading modal
                loadingModal.hide();
                
                // Re-enable button
                checkoutButton.disabled = false;
                checkoutButton.innerHTML = '<i class="fas fa-credit-card"></i> Subscribe Now - ${{ number_format($plan->price, 2) }}';
                
                // Show error
                alert(data.message || 'An error occurred. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            
            // Hide loading modal
            loadingModal.hide();
            
            // Re-enable button
            checkoutButton.disabled = false;
            checkoutButton.innerHTML = '<i class="fas fa-credit-card"></i> Subscribe Now - ${{ number_format($plan->price, 2) }}';
            
            alert('An error occurred. Please try again.');
        });
    });
});
</script>
@endpush
