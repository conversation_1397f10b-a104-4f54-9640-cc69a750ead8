<?php

namespace App\Http\Controllers\Admin;

use App\Models\Plan;
use App\Models\Contact;
use App\Models\Subscription;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\Controller;

class HomeController extends Controller
{
    public function index()
    {
        // Get dashboard counts
        $totalPlans = Plan::count();
        $totalContacts = Contact::count();
        $viewedContacts = Contact::viewed()->count();
        $pendingContacts = Contact::pending()->count();

        // Get subscription and payment counts
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $totalPayments = Payment::count();
        $successfulPayments = Payment::where('status', 'succeeded')->count();

        return view('admin.dashboard', compact(
            'totalPlans',
            'totalContacts',
            'viewedContacts',
            'pendingContacts',
            'totalSubscriptions',
            'activeSubscriptions',
            'totalPayments',
            'successfulPayments'
        ));
    }

    public function showProfile()
    {
        return view('admin.profile');
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $rules = [
            'name'  => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email,' . $user->users_id . ',users_id'],
        ];

        if ($request->filled('password')) {
            $rules['old_password'] = ['required', 'current_password'];
            $rules['password']     = ['required', 'confirmed', 'min:8'];
        }

        $validated = $request->validate($rules);

        $user->name  = $validated['name'];
        $user->email = $validated['email'];

        if (!empty($validated['password'])) {
            $user->password = Hash::make($validated['password']);
        }

        $user->save();

        return back()->with('success', 'Profile updated successfully.');
    }
}
