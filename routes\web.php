<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Guest\MainController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Guest\CommonController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\User\UserController;
use App\Http\Controllers\User\SubscriptionController;
use App\Http\Controllers\Admin\SubscriptionController as AdminSubscriptionController;


Route::get('/cache', function () {
    Artisan::call('optimize:clear');
    return redirect()->route('login')->with('success', 'All caches cleared successfully!');
});

Route::post('logout', [AuthController::class, 'logout'])->name('logout');

// Public routes - admins will be redirected to dashboard
Route::middleware(['admin.redirect'])->group(function () {
    Route::get('/', [MainController::class, 'index'])->name('home');
    Route::get('/about', [MainController::class, 'about'])->name('about');
    Route::get('/terms', [MainController::class, 'terms'])->name('terms');
    Route::get('/privacy', [MainController::class, 'privacy'])->name('privacy');
    Route::get('/pricing', [MainController::class, 'pricing'])->name('pricing');
    Route::get('/contact', [MainController::class, 'contact'])->name('contact');

    // Contact form submission
    Route::post('/contact/submit', [CommonController::class, 'submitContact'])->name('contact.submit');
});

// Auth Routes - only accessible to guests (non-authenticated users)
Route::middleware(['guest.only'])->group(function () {
    Route::get('login', [AuthController::class, 'index'])->name('login');
    Route::get('register', [AuthController::class, 'register'])->name('register');
    Route::post('login-post', [AuthController::class, 'login'])->name('login.post');
    Route::post('register-post', [AuthController::class, 'registerPost'])->name('register.post');
});

Route::middleware(['auth', 'admin.role'])->prefix('admin')->group(function () {
    Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard');
    Route::get('profile', [HomeController::class, 'showProfile'])->name('profile.show');
    Route::put('profile-update', [HomeController::class, 'updateProfile'])->name('profile.update');

    Route::resource('plan', PlanController::class);
    Route::post('plan/{id}/status', [PlanController::class, 'status'])->name('plan.status');
    Route::post('plan/{id}/popular', [PlanController::class, 'popular'])->name('plan.popular');

    // Contact management routes
    Route::resource('contact', ContactController::class)->only(['index', 'show', 'destroy']);
    Route::post('contact/{id}/status', [ContactController::class, 'status'])->name('contact.status');

    // Subscription management routes
    Route::prefix('subscriptions')->name('admin.subscriptions.')->group(function () {
        Route::get('/', [AdminSubscriptionController::class, 'index'])->name('index');
        Route::get('/{id}', [AdminSubscriptionController::class, 'show'])->name('show');
        Route::post('/{id}/toggle-status', [AdminSubscriptionController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Payment management routes
    Route::prefix('payments')->name('admin.payments.')->group(function () {
        Route::get('/', [AdminSubscriptionController::class, 'payments'])->name('index');
        Route::get('/{id}', [AdminSubscriptionController::class, 'showPayment'])->name('show');
    });
});

// User routes (authenticated users only)
Route::middleware(['auth', 'user.role'])->prefix('user')->group(function () {
    Route::get('dashboard', [UserController::class, 'dashboard'])->name('user.dashboard');
    Route::get('profile', [UserController::class, 'profile'])->name('user.profile');
    Route::put('profile', [UserController::class, 'updateProfile'])->name('user.profile.update');
    Route::get('bookings', [UserController::class, 'bookings'])->name('user.bookings');

    // Subscription routes
    Route::prefix('subscriptions')->name('user.subscriptions.')->group(function () {
        Route::get('/', [SubscriptionController::class, 'index'])->name('index');
        Route::get('choose-plan/{planId}', [SubscriptionController::class, 'choosePlan'])->name('choose-plan');
        Route::post('create-checkout-session', [SubscriptionController::class, 'createCheckoutSession'])->name('create-checkout-session');
        Route::get('success', [SubscriptionController::class, 'success'])->name('success');
        Route::get('cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('cancel-subscription', [SubscriptionController::class, 'cancelSubscription'])->name('cancel-subscription');
    });
});
