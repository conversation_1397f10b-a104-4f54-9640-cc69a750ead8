<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Plan;
use App\Models\User;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class SubscriptionController extends Controller
{
    /**
     * Display subscriptions list
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $subscriptions = Subscription::with(['user', 'plan'])
                    ->select('subscription_id', 'user_id', 'plan_id', 'status', 'amount', 'currency', 'interval', 'current_period_start', 'current_period_end', 'auto_renew', 'created_at')
                    ->orderByDesc('created_at')
                    ->get();

                return DataTables::of($subscriptions)
                    ->addColumn('user_info', function ($subscription) {
                        return '<div>' .
                               '<strong>' . $subscription->user->name . '</strong><br>' .
                               '<small class="text-muted">' . $subscription->user->email . '</small>' .
                               '</div>';
                    })
                    ->addColumn('plan_info', function ($subscription) {
                        return '<div>' .
                               '<strong>' . $subscription->plan->name . '</strong><br>' .
                               '<small class="text-muted">$' . number_format($subscription->amount, 2) . '/' . $subscription->interval . '</small>' .
                               '</div>';
                    })
                    ->addColumn('status_badge', function ($subscription) {
                        $statusClass = match($subscription->status) {
                            'active' => 'bg-success',
                            'cancelled' => 'bg-warning',
                            'expired' => 'bg-danger',
                            'past_due' => 'bg-warning',
                            default => 'bg-secondary'
                        };
                        return '<span class="badge ' . $statusClass . '">' . ucfirst($subscription->status) . '</span>';
                    })
                    ->addColumn('period_info', function ($subscription) {
                        if ($subscription->current_period_start && $subscription->current_period_end) {
                            return '<div>' .
                                   '<small>Start: ' . $subscription->current_period_start->format('M d, Y') . '</small><br>' .
                                   '<small>End: ' . $subscription->current_period_end->format('M d, Y') . '</small>' .
                                   '</div>';
                        }
                        return 'N/A';
                    })
                    ->addColumn('auto_renew_status', function ($subscription) {
                        return $subscription->auto_renew
                            ? '<i class="fas fa-check text-success" title="Auto-renew enabled"></i>'
                            : '<i class="fas fa-times text-danger" title="Auto-renew disabled"></i>';
                    })
                    ->addColumn('actions', function ($subscription) {
                        return '<div class="btn-group" role="group">' .
                               '<a href="' . route('admin.subscriptions.show', $subscription->subscription_id) . '" class="btn btn-sm btn-info" title="View Details">' .
                               '<i class="fas fa-eye"></i>' .
                               '</a>' .
                               '<button type="button" class="btn btn-sm btn-warning" onclick="toggleStatus(\'' . $subscription->subscription_id . '\')" title="Toggle Status">' .
                               '<i class="fas fa-toggle-on"></i>' .
                               '</button>' .
                               '</div>';
                    })
                    ->rawColumns(['user_info', 'plan_info', 'status_badge', 'period_info', 'auto_renew_status', 'actions'])
                    ->make(true);
            }

            return view('admin.subscriptions.index');
        } catch (Exception $e) {
            Log::error('Admin subscriptions index error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            if ($request->ajax()) {
                return response()->json(['error' => 'Unable to load subscriptions data.'], 500);
            }

            return back()->with('error', 'Unable to load subscriptions data.');
        }
    }

    /**
     * Show subscription details
     */
    public function show($id)
    {
        try {
            $subscription = Subscription::with(['user', 'plan', 'payments'])
                ->where('subscription_id', $id)
                ->firstOrFail();

            $recentPayments = $subscription->payments()
                ->orderByDesc('created_at')
                ->take(10)
                ->get();

            return view('admin.subscriptions.show', compact('subscription', 'recentPayments'));
        } catch (Exception $e) {
            Log::error('Admin subscription show error', [
                'error' => $e->getMessage(),
                'subscription_id' => $id,
            ]);

            return back()->with('error', 'Subscription not found.');
        }
    }

    /**
     * Display payments list
     */
    public function payments(Request $request)
    {
        try {
            if ($request->ajax()) {
                $payments = Payment::with(['user', 'plan', 'subscription'])
                    ->select('payment_id', 'user_id', 'subscription_id', 'plan_id', 'amount', 'currency', 'status', 'payment_method', 'description', 'paid_at', 'created_at')
                    ->orderByDesc('created_at')
                    ->get();

                return DataTables::of($payments)
                    ->addColumn('user_info', function ($payment) {
                        return '<div>' .
                               '<strong>' . $payment->user->name . '</strong><br>' .
                               '<small class="text-muted">' . $payment->user->email . '</small>' .
                               '</div>';
                    })
                    ->addColumn('plan_info', function ($payment) {
                        return '<div>' .
                               '<strong>' . $payment->plan->name . '</strong><br>' .
                               '<small class="text-muted">' . ($payment->description ?? 'N/A') . '</small>' .
                               '</div>';
                    })
                    ->addColumn('amount_formatted', function ($payment) {
                        return '$' . number_format($payment->amount, 2) . ' ' . strtoupper($payment->currency);
                    })
                    ->addColumn('status_badge', function ($payment) {
                        $statusClass = match($payment->status) {
                            'succeeded' => 'bg-success',
                            'pending' => 'bg-warning',
                            'failed' => 'bg-danger',
                            'refunded' => 'bg-info',
                            'cancelled' => 'bg-secondary',
                            default => 'bg-secondary'
                        };
                        return '<span class="badge ' . $statusClass . '">' . ucfirst($payment->status) . '</span>';
                    })
                    ->addColumn('payment_date', function ($payment) {
                        return $payment->paid_at ? $payment->paid_at->format('M d, Y g:i A') : 'N/A';
                    })
                    ->addColumn('actions', function ($payment) {
                        return '<a href="' . route('admin.payments.show', $payment->payment_id) . '" class="btn btn-sm btn-info" title="View Details">' .
                               '<i class="fas fa-eye"></i>' .
                               '</a>';
                    })
                    ->rawColumns(['user_info', 'plan_info', 'status_badge', 'actions'])
                    ->make(true);
            }

            return view('admin.payments.index');
        } catch (Exception $e) {
            Log::error('Admin payments index error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            if ($request->ajax()) {
                return response()->json(['error' => 'Unable to load payments data.'], 500);
            }

            return back()->with('error', 'Unable to load payments data.');
        }
    }

    /**
     * Show payment details
     */
    public function showPayment($id)
    {
        try {
            $payment = Payment::with(['user', 'plan', 'subscription'])
                ->where('payment_id', $id)
                ->firstOrFail();

            return view('admin.payments.show', compact('payment'));
        } catch (Exception $e) {
            Log::error('Admin payment show error', [
                'error' => $e->getMessage(),
                'payment_id' => $id,
            ]);

            return back()->with('error', 'Payment not found.');
        }
    }

    /**
     * Toggle subscription status
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $subscription = Subscription::where('subscription_id', $id)->firstOrFail();

            $newStatus = $subscription->status === 'active' ? 'cancelled' : 'active';

            $subscription->update([
                'status' => $newStatus,
                'cancelled_at' => $newStatus === 'cancelled' ? now() : null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription status updated successfully.',
                'new_status' => $newStatus,
            ]);
        } catch (Exception $e) {
            Log::error('Admin subscription status toggle error', [
                'error' => $e->getMessage(),
                'subscription_id' => $id,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unable to update subscription status.',
            ], 500);
        }
    }
}
