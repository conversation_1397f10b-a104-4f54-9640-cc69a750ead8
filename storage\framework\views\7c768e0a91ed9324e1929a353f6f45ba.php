<?php $__env->startSection('content'); ?>
<?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Payments'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-md-12">
            <div class="card mb-4">
                <div class="d-flex justify-content-between px-3 py-4">
                    <h6 class="mb-0" style="color: #67748e;">Payment History</h6>
                    <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                        <i class="fas fa-users me-1"></i> View Subscriptions
                    </a>
                </div>

                <div class="card-body p-3">
                    <?php if (isset($component)) { $__componentOriginalc8463834ba515134d5c98b88e1a9dc03 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.data-table','data' => ['id' => 'paymentsTable','ajax' => route('admin.payments.index'),'columns' => [
                        [
                            'data' => 'DT_RowIndex',
                            'name' => 'DT_RowIndex',
                            'orderable' => false,
                            'searchable' => false,
                        ],
                        ['data' => 'user_info', 'name' => 'user.name'],
                        ['data' => 'plan_info', 'name' => 'plan.name'],
                        ['data' => 'amount_formatted', 'name' => 'amount'],
                        ['data' => 'status_badge', 'name' => 'status'],
                        ['data' => 'payment_date', 'name' => 'paid_at'],
                        ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                    ],'order' => [[5, 'desc']]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'paymentsTable','ajax' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.payments.index')),'columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        [
                            'data' => 'DT_RowIndex',
                            'name' => 'DT_RowIndex',
                            'orderable' => false,
                            'searchable' => false,
                        ],
                        ['data' => 'user_info', 'name' => 'user.name'],
                        ['data' => 'plan_info', 'name' => 'plan.name'],
                        ['data' => 'amount_formatted', 'name' => 'amount'],
                        ['data' => 'status_badge', 'name' => 'status'],
                        ['data' => 'payment_date', 'name' => 'paid_at'],
                        ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                    ]),'order' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([[5, 'desc']])]); ?>
                         <?php $__env->slot('header', null, []); ?> 
                            <th>S.No.</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment Date</th>
                            <th>Actions</th>
                         <?php $__env->endSlot(); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $attributes = $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $component = $__componentOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/payments/index.blade.php ENDPATH**/ ?>