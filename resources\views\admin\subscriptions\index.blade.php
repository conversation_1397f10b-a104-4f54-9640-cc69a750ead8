@extends('layouts.app')

@section('content')
@include('layouts.navbars.auth.topnav', ['title' => 'Subscriptions'])
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title">
                            <i class="fas fa-users"></i> User Subscriptions
                        </h3>
                        <div class="card-tools">
                            <a href="{{ route('admin.payments.index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-credit-card"></i> View Payments
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table id="subscriptions-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Status</th>
                                    <th>Period</th>
                                    <th>Auto Renew</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#subscriptions-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: '{{ route("admin.subscriptions.index") }}',
        columns: [
            { data: 'user_info', name: 'user.name', orderable: false, searchable: true },
            { data: 'plan_info', name: 'plan.name', orderable: false, searchable: true },
            { data: 'status_badge', name: 'status', orderable: true, searchable: true },
            { data: 'period_info', name: 'current_period_start', orderable: false, searchable: false },
            { data: 'auto_renew_status', name: 'auto_renew', orderable: true, searchable: false },
            { data: 'created_at', name: 'created_at', orderable: true, searchable: false },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[5, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            processing: '<i class="fas fa-spinner fa-spin"></i> Loading subscriptions...'
        }
    });
});

function toggleStatus(subscriptionId) {
    if (confirm('Are you sure you want to toggle this subscription status?')) {
        $.ajax({
            url: `/admin/subscriptions/${subscriptionId}/toggle-status`,
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#subscriptions-table').DataTable().ajax.reload();
                    
                    // Show success toast
                    const toast = `
                        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="d-flex">
                                <div class="toast-body">
                                    ${response.message}
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                            </div>
                        </div>
                    `;
                    
                    // Add toast to page and show it
                    $('body').append(`<div class="toast-container position-fixed top-0 end-0 p-3">${toast}</div>`);
                    $('.toast').toast('show');
                    
                    // Remove toast container after it's hidden
                    $('.toast').on('hidden.bs.toast', function() {
                        $(this).closest('.toast-container').remove();
                    });
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('An error occurred while updating the subscription status.');
            }
        });
    }
}
</script>
@endpush
