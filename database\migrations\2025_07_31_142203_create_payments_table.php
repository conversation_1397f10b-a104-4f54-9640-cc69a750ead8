<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->uuid('payment_id')->primary();
            $table->uuid('user_id');
            $table->uuid('subscription_id')->nullable();
            $table->uuid('plan_id');
            $table->string('stripe_payment_intent_id')->unique()->nullable();
            $table->string('stripe_charge_id')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('usd');
            $table->string('status'); // succeeded, pending, failed, cancelled, refunded
            $table->string('payment_method')->nullable(); // card, bank_transfer, etc.
            $table->string('description')->nullable();
            $table->json('stripe_response')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->decimal('refunded_amount', 10, 2)->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('users_id')->on('users')->onDelete('cascade');
            $table->foreign('subscription_id')->references('subscription_id')->on('subscriptions')->onDelete('set null');
            $table->foreign('plan_id')->references('plans_id')->on('plans')->onDelete('cascade');

            $table->index(['user_id', 'status']);
            $table->index('stripe_payment_intent_id');
            $table->index('subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
