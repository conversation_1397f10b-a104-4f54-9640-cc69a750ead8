<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    /**
     * Show user dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Get user-specific data here
        // For example: bookings, orders, etc.
        
        return view('user.dashboard', compact('user'));
    }

    /**
     * Show user profile
     */
    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        // Check if password fields are being updated to determine if password form should stay open
        $isPasswordUpdate = $request->filled('current_password') || $request->filled('new_password');

        try {
            $validatedData = $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'email' => ['required', 'email', 'max:255', 'unique:users,email,' . $user->users_id . ',users_id'],
                'mobile' => ['nullable', 'string', 'max:20', 'regex:/^[+\-\s\(\)]*[0-9][0-9+\-\s\(\)]*$/', function ($attribute, $value, $fail) {
                    if ($value && preg_match_all('/[0-9]/', $value) < 10) {
                        $fail('Mobile number must contain at least 10 digits.');
                    }
                }],
                'image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
                'current_password' => ['nullable', 'required_with:new_password'],
                'new_password' => [
                    'nullable',
                    'confirmed',
                    Password::min(8)->mixedCase()->letters()->numbers()->symbols()->max(20)
                ],
            ], [
                'mobile.min' => 'Mobile number must be at least 10 digits long.',
                'mobile.regex' => 'Mobile number can only contain numbers, spaces, +, -, ( and ) characters.',
                'new_password.confirmed' => 'New password confirmation does not match.',
                'current_password.required_with' => 'Current password is required when changing password.',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors = $e->validator->errors()->all();
            $errorMessage = implode(' ', $errors);

            return back()
                ->withInput()
                ->with('error', $errorMessage)
                ->with('show_password_form', $isPasswordUpdate);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($user->image && Storage::disk('public')->exists($user->image)) {
                Storage::disk('public')->delete($user->image);
            }

            // Store new image
            $imagePath = $request->file('image')->store('profile-images', 'public');
            $user->image = $imagePath;
        }

        // Update basic info
        $user->name = $validatedData['name'];
        $user->email = $validatedData['email'];
        $user->mobile = $validatedData['mobile'] ?? $user->mobile;

        // Check if password fields are being updated
        $isPasswordUpdate = !empty($validatedData['current_password']) || !empty($validatedData['new_password']);

        // Update password if provided
        if (!empty($validatedData['current_password']) && !empty($validatedData['new_password'])) {
            if (!Hash::check($validatedData['current_password'], $user->password)) {
                return back()
                    ->withInput()
                    ->with('error', 'Current password is incorrect.')
                    ->with('show_password_form', true);
            }

            $user->password = Hash::make($validatedData['new_password']);
        }

        $user->save();

        $message = 'Profile updated successfully!';
        if ($isPasswordUpdate && !empty($validatedData['new_password'])) {
            $message = 'Profile and password updated successfully!';
        }

        return back()->with('success', $message);
    }

    /**
     * Show user bookings (placeholder)
     */
    public function bookings()
    {
        $user = Auth::user();
        
        // Get user bookings here
        // $bookings = Booking::where('user_id', $user->id)->get();
        
        return view('user.bookings', compact('user'));
    }
}
