@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>My Subscriptions</h2>
                <a href="{{ route('pricing') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Browse Plans
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('info'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    {{ session('info') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Current Subscription -->
            @if($activeSubscription)
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle"></i> Current Subscription
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>{{ $activeSubscription->plan->name }}</h4>
                                <p class="text-muted">{{ $activeSubscription->plan->description }}</p>
                                
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Amount:</strong> ${{ number_format($activeSubscription->amount, 2) }}
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Billing:</strong> {{ ucfirst($activeSubscription->interval) }}
                                    </div>
                                </div>
                                
                                <div class="row mt-2">
                                    <div class="col-sm-6">
                                        <strong>Status:</strong> 
                                        <span class="badge bg-success">{{ ucfirst($activeSubscription->status) }}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Next Billing:</strong> 
                                        {{ $activeSubscription->current_period_end ? $activeSubscription->current_period_end->format('M d, Y') : 'N/A' }}
                                    </div>
                                </div>

                                @if($activeSubscription->auto_renew)
                                    <div class="mt-3">
                                        <i class="fas fa-sync text-success"></i>
                                        <small class="text-success">Auto-renewal is enabled</small>
                                    </div>
                                @endif
                            </div>
                            <div class="col-md-4 text-end">
                                @if($activeSubscription->isActive())
                                    <form method="POST" action="{{ route('user.subscriptions.cancel-subscription') }}" 
                                          onsubmit="return confirm('Are you sure you want to cancel your subscription? You will continue to have access until the end of your billing period.')">
                                        @csrf
                                        <button type="submit" class="btn btn-outline-danger">
                                            <i class="fas fa-times"></i> Cancel Subscription
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="card mb-4">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h4>No Active Subscription</h4>
                        <p class="text-muted">You don't have any active subscription. Browse our plans to get started!</p>
                        <a href="{{ route('pricing') }}" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Plans
                        </a>
                    </div>
                </div>
            @endif

            <!-- Subscription History -->
            @if($subscriptionHistory->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i> Subscription History
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Plan</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Period</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($subscriptionHistory as $subscription)
                                        <tr>
                                            <td>
                                                <strong>{{ $subscription->plan->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ ucfirst($subscription->interval) }}</small>
                                            </td>
                                            <td>${{ number_format($subscription->amount, 2) }}</td>
                                            <td>
                                                @php
                                                    $statusClass = match($subscription->status) {
                                                        'active' => 'bg-success',
                                                        'cancelled' => 'bg-warning',
                                                        'expired' => 'bg-danger',
                                                        default => 'bg-secondary'
                                                    };
                                                @endphp
                                                <span class="badge {{ $statusClass }}">
                                                    {{ ucfirst($subscription->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($subscription->current_period_start && $subscription->current_period_end)
                                                    {{ $subscription->current_period_start->format('M d') }} - 
                                                    {{ $subscription->current_period_end->format('M d, Y') }}
                                                @else
                                                    N/A
                                                @endif
                                            </td>
                                            <td>{{ $subscription->created_at->format('M d, Y') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Payment History -->
            @if($paymentHistory->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card"></i> Recent Payments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Plan</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Payment Date</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($paymentHistory as $payment)
                                        <tr>
                                            <td>{{ $payment->plan->name }}</td>
                                            <td>${{ number_format($payment->amount, 2) }}</td>
                                            <td>
                                                @php
                                                    $statusClass = match($payment->status) {
                                                        'succeeded' => 'bg-success',
                                                        'pending' => 'bg-warning',
                                                        'failed' => 'bg-danger',
                                                        'refunded' => 'bg-info',
                                                        default => 'bg-secondary'
                                                    };
                                                @endphp
                                                <span class="badge {{ $statusClass }}">
                                                    {{ ucfirst($payment->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                {{ $payment->paid_at ? $payment->paid_at->format('M d, Y g:i A') : 'N/A' }}
                                            </td>
                                            <td>{{ $payment->description ?? 'N/A' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
