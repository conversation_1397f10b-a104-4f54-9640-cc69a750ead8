@extends('layouts.app')

@section('content')
@include('layouts.navbars.auth.topnav', ['title' => 'Payments'])

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-md-12">
            <div class="card mb-4">
                <div class="d-flex justify-content-between px-3 py-4">
                    <h6 class="mb-0" style="color: #67748e;">Payment History</h6>
                    <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                        <i class="fas fa-users me-1"></i> View Subscriptions
                    </a>
                </div>

                <div class="card-body p-3">
                    <x-data-table id="paymentsTable" :ajax="route('admin.payments.index')" :columns="[
                        [
                            'data' => 'DT_RowIndex',
                            'name' => 'DT_RowIndex',
                            'orderable' => false,
                            'searchable' => false,
                        ],
                        ['data' => 'user_info', 'name' => 'user.name'],
                        ['data' => 'plan_info', 'name' => 'plan.name'],
                        ['data' => 'amount_formatted', 'name' => 'amount'],
                        ['data' => 'status_badge', 'name' => 'status'],
                        ['data' => 'payment_date', 'name' => 'paid_at'],
                        ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                    ]" :order="[[5, 'desc']]">
                        <x-slot:header>
                            <th>S.No.</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment Date</th>
                            <th>Actions</th>
                        </x-slot:header>
                    </x-data-table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection


